# 前言

 本项目是一个nuxt3的企业级官网模板项目，开箱即用，直接下载依赖就可以运行。

**优势：**

1、采用vue3+Nuxt3最新技术，具有较高的网页渲染性能。

2、SSR渲染页面，有利于官网SEO，提高关键词在搜索引擎的排名。而且支持中英文、不同语种的SEO。

3、集成了**tailwindcss**、**vuetify**、**i18n**等优秀中间件。

4、屏幕尺寸自适应，无论是电脑、笔记本、平板、手机屏幕都能自适应显示，可一镜到底。

---

**技术选型或开发环境**

| 技术/版本             | 介绍                                                         | 开发文档                                             |
| --------------------- | ------------------------------------------------------------ | ---------------------------------------------------- |
| **vue 3.4**           | 用于构建用户界面的 JavaScript 框架                           | https://cn.vuejs.org/guide/introduction.html         |
| **nuxt 3.11**         | 基于vue的SSR框架                                             | https://nuxt.com.cn/docs/guide/concepts/rendering    |
| **vuetify 3.6**       | 基于vue的UI框架，具有响应式UI布局                            | https://vuetifyjs.com/zh-Hans                        |
| **tailwindcss 3.4.3** | 用于写CSS样式的中间件                                        | https://tailwind.nodejs.cn/docs/installation         |
| **Typescript**        | 一种基于 JavaScript 构建的强类型编程语言                     | https://ts.nodejs.cn/#google_vignette                |
| **vite 5.2.11**       | 前端开发与构建工具                                           | https://vitejs.cn/                                   |
| **pinia 2.1.7**       | 一个拥有[组合式 API](https://github.com/vuejs/composition-api) 的 Vue3 状态管理库 | https://pinia.vuejs.org/zh/                          |
| **i18n 9.13**         | 做国际化，添加多国语言切换                                   | https://vue-i18n.intlify.dev/guide/introduction.html |
| **xgplayer 3.0.17**   | 播放器插件                                                   | https://v2.h5player.bytedance.com/gettingStarted/    |
| **Node.js v20.12.2**  | 开发环境                                                     | https://nodejs.org/zh-cn                             |

# 项目预览

[https://shop.tearful.cn/](https://shop.tearful.cn/)



![主页](http://cdn.tearful.cn/shop/preview/65122a36-ae2b-4109-ad62-18718a8cfcec.png)



![主页](http://cdn.tearful.cn/shop/preview/4e2b2806-02dd-4cc6-8188-23de33bb779a.png)



![主页](http://cdn.tearful.cn/shop/preview/74d8560e-7494-44c8-83cd-4805fec884ec.png)



![主页](http://cdn.tearful.cn/shop/preview/b36ac1be-5e71-49d5-ba6e-9d8c5c02dc73.png)



欢迎反馈、讨论、提交PR！

# 项目考虑

如果说一个新的项目要考虑什么、前期要做哪些准备？这是我们身为一名开发者要考虑的事情，这涉及到未来我们的网站功能会不会用到和可扩展性。当然，也可以用到之后再进行添加，比如Chart.js。

接下来聊一聊前端项目一些基本的功能要求：

1、定框架的大体方向。是选择Vue、React、Angular还是其他的框架，这个基本都是根据开源社区的活跃度和开发人员的技能掌握来决定。为什么说开源社区很重要呢？一方面是框架有缺陷会及时修复，另一方面是我们遇到问题之后很容易就能找到解决方案，如果说你遇到的问题是从来没有出现过的，**那么恭喜你，你已经成为这款框架中极为顶尖的存在了！**

> 推荐VUE

2、UI框架的选择。我们当然可以自己手写html和CSS，但是对于开发人员来说会耗费极大的时间去造轮子，从效率来说也很低。所以我们需要寻找一款适合自己的UI框架，美观、使用简单、拥抱前端框架（vue、react等）。还有就是，如果考虑到官网最SEO的情况，最好使用Nuxt3

> 推荐vuetify、Tailwindcss、Nuxt3.这些都需要在前期考虑，开发到一半再插入可能会动摇整个项目根基（要是报错就爽歪歪了）。

3、如果需要后端接口，那么Axios、Pinia也是必不可少的插件，如果有国际化的要求，让你的网站走向世界，那么加入i18n是一个不错的选择。

4、其他的工具类插件：day.js、useVue

# 要点

## 开发提示

### 1、nuxt3路由跳转

**方法1**

```ts
// https://nuxt.com.cn/docs/api/composables/use-router
const router = useRouter()

router.back()				//返回上一页 与 router.go(-1) 相同。
router.forward()			//前进到下一页，与 router.go(1)
router.go(3)				// 前进3页
router.push({ path: "/home" })	//通过将新条目推入历史堆栈来以编程方式导航到新的 URL。建议使用 navigateTo 代替。
router.replace({ hash: "#bio" }) //通过替换当前路由历史堆栈中的当前条目来以编程方式导航到新的 URL。建议使用 navigateTo 代替。

```

**方法2(推荐)**

```ts
// https://nuxt.com.cn/docs/api/utils/navigate-to
<script setup lang="ts">
// 将'to'作为字符串传递
await navigateTo('/search')

// ... 或者作为路由对象传递
await navigateTo({ path: '/search' })

// ... 或者作为带有查询参数的路由对象传递
await navigateTo({
  path: '/search',
  query: {
    page: 1,
    sort: 'asc'
  }
})
</script>
```



## 待解决

1、手机端自适应还可以做更好；

2、加载图片的时候，等待动画做的不够优美，高度暂时是固定写死的；

3、字体不够好看，要集成自定义字体到网站中。

# 开发文档

https://nuxt.com.cn/docs/guide/concepts/rendering 				nuxt3中文文档1

https://ezdoc.cn/docs/nuxtjs/composables/use-fetch 				nuxt3中文文档2

https://www.bossdesign.cn/clipdrop/											图片工具

https://tailwind.nodejs.cn/docs/installation 								tailwindcss中文文档

https://vuetifyjs.com/zh-Hans/getting-started/installation	  vuetifyjs官网

## 组件分享

https://tailwindcomponents.com/component/sticky-section	轮播图

# Nuxt 3 Minimal Starter

Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

https://www.albanypark.com/

## Setup

Make sure to install the dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm run build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm run preview

# yarn
yarn preview

# bun
bun run preview
```

8wxqqlzi/adaa7b0e

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

# 部署

## 在开发环境下运行打包好的文件

进入./.output/server目录下运行

```shell
node index.mjs
```

## 使用docker部署（推荐）

1、拉取镜像文件

下载最新的node镜像，我那时候docker最新的node版本是17.3.0

```shell
docker pull node
```

2、启动容器（在/www/wwwroot/shop.tearful.cn目录文件下执行）

```shell
docker run -ti -p 3001:3000 --name node-nuxt3-shop -v $(pwd):/workspace node bash
```

> 解释： **node-nuxt3-shop**是给容器起的名字；
>
> 3001:3000 是将宿主机的3001端口映射到容器内部的3000端口
>
> -v $(pwd):/workspace 是将当前目录挂载到容器的/workspace目录（也可以直接写绝对路径）
>
> node 是镜像的名字
>
> bash 是进入容器之后使用bash命令解释器

**注意：**以上这个命令是启动并运行容器，但是退出之后可能会发现容器没有运行起来，这样子的话运行容器即可：

```shell
$ docker start node-nuxt3-shop
```

3、进入运行中的**nuxt3_shop_node容器**查看版本

```shell
docker exec -it nuxt3_shop_node bash
```

4、安装pm2

```shell
yarn global add pm2
```

5、在容器中进入工作文件

进入node-nuxt3-shop容器

```shell
docker exec -it node-nuxt3-shop bash
```

切换到工作目录

```shell
cd /workspace
```

---

以上是服务器准备好了，接下来回到我们的代码。

## 启动 nuxt.js 应用[](https://pm2.fenxianglu.cn/docs/start#启动-nuxtjs-应用)

在项目根目录创建 `ecosystem.config.js` 文件并配置：

```js
module.exports = {
    apps: [
        {
            name: 'shop_nuxt3',  //名称可以自定义
            exec_mode: 'cluster', // 集群模式
            instances: 'max', // Or a number of instances
            script: './output/server/index.mjs',
            port: '3000',
        }
    ]
}
```

确认项目无误之后打包

```shell
yarn build
```

然后把.output改为output，最后把.output文件夹上传到服务器/www/wwwroot/shop.tearful.cn文件夹下。所对应的ecosystem.config.js配置为

```js
script: './output/server/index.mjs', // 不是写错，而是故意写./output的
```

> 注意：这里有一个小坑，docker容器目录挂载识别不到.output，但是可以识别output，所以上传到服务器之前改一下文件名为output

在服务器，请看步骤3方法，进入容器内部，切换到/workspace目录下并执行

```shell
$ pm2 start ecosystem.config.js
```

PM2常用命令：

```shell
$ pm2 start ecosystem.config.js		# 启动配置文件执行应用程序
$ pm2 reload ecosystem.config.js	# 重新加载应用程序文件
$ pm2 restart ecosystem.config.js	# 重启应用程序
$ pm2 stop all						# 停止所有应用程序
$ pm2 list							# 查看运行的进程

```

进入容器命令：

```shell
docker exec -it 775c7c9ee1e1（容器ID/容器名称） /bin/bash
#例如 docker exec -it node-nuxt3-shop bash
```

## 配置Ngixn反向代理

```shell
server
{
    listen 80;
    server_name shop.tearful.cn;
    index index.php index.html index.htm default.php default.htm default.html;
    root /www/wwwroot/shop.tearful.cn;
    # http重定向到https
    if ($server_port !~ 443){
    	rewrite ^(/.*)$ https://$host$1 permanent;
      	return 307 https://${server_name}$request_uri;
    }
    # 这里一定要用正则表达式，不然请求资源的时候会报404
    location ~ ^/ {
      proxy_pass http://localhost:3001;
    }
}
```

 ## 部署总结

对于整个链路来说，每一个组件都各司其职，Linux作为宿主机运行，Nginx作为反向代理功能，安全交给SSL证书即可，docker作为容器将服务之间分隔开，服务器迁移的时候也比较简单。

docker容器里面PM2作为进程管理器，在本项目中只做了一件事：启动nuxt项目，其实它还可以做更多的事情，官网：https://pm2.keymetrics.io/docs/usage/quick-start/，中文文档：https://pm2.fenxianglu.cn/docs/start。nuxt做了SSR和SEO，这完全做到了什么组件干了什么事情。

从前端的http请求 上来说，http从前端发起请求，会带上域名（shop.tearful.cn）、端口号（80/443），然后经过DNS域名解析找到服务器，进入linux宿主机的时候由于nginx监听了80端口，所以查询配置文件（应该把你的请求带到哪里去），由下面的配置可知，所以请求到端口3001。如果找不到对应的Nginx配置，就会返回前端一个404。

```shell
    listen 80;
    server_name shop.tearful.cn;
```

3001正好是docker容器名叫node-nuxt3-shop的服务所监听，然后请求就到了容器这里，然后呢，容器对外是3001端口，映射的是容器内部的3000端口，所以请求就进入容器内部的3000端口，也就访问到了nuxt项目（vue项目）服务了。整个流程已经走完，返回的时候就原路返回即可。

