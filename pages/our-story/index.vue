<script setup lang="ts">
import {useUserPinia} from "~/stores/user";
import {computed} from "vue";

const {locale, setLocale, tm} = useI18n()

const qualityIcons = computed(() => {
  return tm("videos.qualityIcons")
})
const our_story_items = computed(() => {
  return tm("our_story.items")
})

const buyers = computed(() => {
  return tm("shop.body.buyer")
})

const seo_title = computed(() => {
  return tm("our_story.seo.title");
})
const seo_ogDescription = computed(() => {
  return tm("our_story.seo.ogDescription");
})

// SEO 会根据中英文切换做不同的SEO
useSeoMeta({
  title: seo_title,
  ogDescription: seo_ogDescription,
})

</script>

<template>
  <v-app>
    <v-sheet color="#F5F4F2">
      <v-sheet class="pt-[72px]">
        <banner-image HeadImage="https://tearful-cn.oss-cn-shenzhen.aliyuncs.com/shop/our-story/AP_OurStory_v3.jpg"/>
      </v-sheet>

      <!--      第一个卡片文字-->
      <v-sheet color="transparent" class="d-flex justify-center py-8">
        <div>
          <v-sheet v-for="item in our_story_items" :key="item.id" color="transparent" max-width="720" class="pt-4">
            <v-card color="transparent" flat class="px-9">
              <h1 class="text-[#413E3A] font-serif text-3xl md:text-4xl lg:text-5xl py-4">
                {{ item.title }}
              </h1>
              <p v-for="text in item.text" class="py-4 text-[#413E3A] text-md font-weight-medium leading-6">{{ text }}
              </p>
              <!-- 条件显示-->
              <p v-if="item.id===3" class="py-4 text-[#413E3A] text-md font-weight-bold leading-6">{{
                  item.special
                }} </p>
            </v-card>
          </v-sheet>
        </div>

      </v-sheet>

      <!--      图片-->
      <v-sheet class="" color="transparent">
        <banner-image HeadImage="https://oss-cdn.tearful.cn/shop/our-story/ourstory_yea.webp"/>
      </v-sheet>
    </v-sheet>

    <!--  八个推荐说明图标-->
    <v-sheet color="#F5F4F2">
      <h1 class="py-16 text-[#413E3A] text-center font-buttershine-serif text-[#413e3a] text-2xl lg:text-3xl">
        {{ $t('shop.body.myAlbanyPark.icon_title') }}
      </h1>
      <div class="d-flex justify-center ">
        <v-sheet color="transparent" max-width="1100">
          <div class="pb-8 ">
            <v-row class="">
              <v-col v-for="item in qualityIcons" :key="item.id" class="d-flex justify-center pa-0">
                <v-card class="px-16 py-8" color="transparent"
                        flat>
                  <div class="d-flex justify-center">
                    <div class="pb-8">
                      <v-img cover width="105" :src="item.url"/>
                    </div>
                  </div>
                  <div>
                    <h1 class="text-center"> {{ item.title }}</h1>
                  </div>
                </v-card>
              </v-col>
            </v-row>
          </div>
        </v-sheet>
      </div>
    </v-sheet>

    <!--    两个沙发图标-->
    <v-sheet color="#EEE9E3" class="">
      <sofa-show :Items="buyers"/>
    </v-sheet>

    <!--      图片-->
    <v-sheet color="transparent">
      <banner-image link HeadImage="https://oss-cdn.tearful.cn/shop/our-story/our-story-image.webp"/>
    </v-sheet>

  </v-app>
</template>

<style scoped>

</style>