<script setup lang="ts">
import {useUserPinia} from '~/stores/user'
import {storeToRefs} from "pinia";
import {onMounted, ref, onBeforeMount} from "vue";
import HP5050 from "~/public/shop/HP5050_banner-Lido_03_Ivory_Boucle_1011-992x1120_672x448.webp"
// 用tm代替t，因为tm是响应式的而且可以解析对象
const {locale, setLocale, tm} = useI18n()
const userStore = useUserPinia()
const {token, rating_five} = storeToRefs(userStore);

// import Player from "xgplayer";
// import "xgplayer/dist/index.min.css";
// onMounted(() => {
//   new Player({
//     id: "mse", //元素id
//     lang: "zh", //设置中文
//     volume: 0, // 默认静音
//     autoplay: false, //自动播放
//     screenShot: true, // 开启截图功能
//
//     //视频地址
//     url: "https://cdn.free-stock.video/25122023/abstract-curve-chaos-paint-ink-that-design-91614-small.mp4",
//
//     //封面图
//     poster:
//         "//lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/byted-player-videos/1.0.0/poster.jpg",
//     fluid: true, // 填满屏幕 （流式布局）
//     playbackRate: [0.5, 0.75, 1, 1.5, 2] //传入倍速可选数组
//   });
// });
/**
 * 屏幕尺寸
 */
const screenSize = ref({width: 0, height: 0});

/**
 * 图片是否加载完成
 */
const home_image1_load = ref(false)

/**
 * 获取组元素组件的宽度
 */
const getGroupWidth: any = computed(() => {
  if (screenSize.value.width > 1470) {
    return 1440;
  } else if (screenSize.value.width > 1008) {
    return 1000;
  } else {
    return 560;
  }
})

/**
 * 响应式i18n
 */
const image_icons = computed(() => {
  return tm("shop.body.choose.shopIcons");
})

const style_images = computed(() => {
  return tm("shop.body.choose.shopList")
})
const qualityIcons = computed(() => {
  return tm("shop.body.qualityIcons")
})
const buyers = computed(() => {
  return tm("shop.body.buyer")
})
const populars = computed(() => {
  return tm("shop.body.popular")
})
const brandIcons = computed(() => {
  return tm("shop.body.brandIcons")
})
const photo_images = computed(() => {
  return tm("shop.body.photos")
})

// 更新屏幕尺寸的方法
const updateScreenSize = () => {
  screenSize.value = {
    width: window.innerWidth,
    height: window.innerHeight,
  };
  // console.log("屏幕尺寸", screenSize.value.width)
};

onMounted(() => {
  window.addEventListener('resize', updateScreenSize);
  // 初始化屏幕尺寸
  updateScreenSize();
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize);
});

/**
 * 图片加载完成回调
 * @param val 图片地址
 */
const image_load = (val: any) => {
  // console.log("Image load successfully")
  home_image1_load.value = true;
}

</script>

<template>
  <v-app>
    <!-- 主图-->
    <v-sheet class="pt-[72px]">
      <banner-image link to="/collections/kova/products/kova-l-shape-ottoman"
                    HeadImage="https://oss-cdn.tearful.cn/shop/banner/AP_memorial_day_sale_BANNERSv2-01_2048x2048.webp"
                    SubImage="https://oss-cdn.tearful.cn/shop/banner/AP_memorial_day_sale_BANNERSv2-02_800x.webp"/>
    </v-sheet>

    <!--    合作企业商标-->
    <v-sheet class="d-flex justify-center align-center my-2  flex-wrap py-6">
      <v-card
          width="192"
          elevation="0"
          class="rounded-0 text-center py-8">
        <div class="text-[#413E3A] ">
          {{ $t("shop.body.featured") }}
        </div>
      </v-card>
      <v-card
          elevation="0"
          class="rounded-0 px-4 ">
        <v-img width="192" cover src="~/public/shop/AD_192x64.webp"/>
      </v-card>

      <v-card
          elevation="0"
          class="rounded-0 pa-4">
        <v-img width="192" cover src="~/public/shop/FRB_2d5a1fd8-30eb-4312-81b7-8dd7b1c90391_192x64.webp"/>
      </v-card>
      <v-card
          elevation="0"
          class="rounded-0 pa-4">
        <v-img width="192" cover src="~/public/shop/hgtv1_192x64.webp"/>
      </v-card>
    </v-sheet>

    <!--    选择你喜爱的风格-->
    <v-sheet color="#EEE9E3" class="d-flex flex-column pt-8 pb-10">
      <div class="text-center pt-16">
        <p class="font-buttershine-serif text-[#413e3a] text-3xl lg:text-4xl">{{ $t('shop.body.choose.title1') }} </p>
        <p class="text-sm font-light py-3 tracking-tight tracking-wide"> {{ $t('shop.body.choose.title2') }}</p>
      </div>

      <!--幻灯组 https://vuetifyjs.com/zh-Hans/components/slide-groups -->
      <v-sheet
          class="mx-auto w-[100%] sm:w-[555px] md:w-[990px] xl:w-[1438px]"
          elevation="0"
          color="transparent"
      >
        <v-slide-group
            class="pt-4 pb-0 "
            selected-class="bg-primary"
            mandatory
            show-arrows
        >
          <v-slide-group-item
              v-for="item in style_images"
              v-slot="{ isSelected, toggle, selectedClass }"
          >
            <v-card
                elevation="0"
                rounded="0"
                link
                :class="['ma-3', selectedClass]"
                color="#F5F4F2"
                class="sm:w-[425px] w-[340px]"
            >
              <v-img :src="item.imageUrl"></v-img>

              <div class="text-center">
                <div class="pt-6 text-xl font-semibold text-[#413e3a]"> {{ item.title }}</div>
                <v-row class="d-flex align-center justify-center pt-2">
                  <div class="text-center">
                    <v-rating
                        v-model="item.star"
                        size="small"
                        readonly
                        half-increments
                        active-color="#bf9b81"
                        color="#C1C1C1"
                        density="compact"
                    ></v-rating>
                  </div>
                  <span class="font-thin text-xs pl-1">{{ item.star }}</span></v-row>
                <div class="font-thin text-xs pt-2">Sofa starts at <span class="font-semibold">{{ item.price }}</span>
                </div>
                <div class="font-thin text-xs italic py-1">or {{ item.monthPrice }}/month</div>
                <div class="font-thin text-xs py-1"> {{ item.fabric }}</div>
                <div class="font-thin text-xs"> {{ item.leg }}</div>
                <div class="pt-4 mb-10 text-[#786554] underline underline-offset-8"> {{ item.shop }}</div>
              </div>
            </v-card>
          </v-slide-group-item>
        </v-slide-group>
      </v-sheet>
    </v-sheet>

    <!--    Park展示-->
    <v-sheet color="#F5F4F2" class="pb-6">
      <div class="text-center pt-16">
        <p class="text-[#413E3A] font-buttershine-serif text-2xl lg:text-3xl"><span
            class="font-weight-bold">#</span>{{ $t('shop.body.myAlbanyPark.title') }}
        </p>
        <p class="pt-3 pb-6 text-stone-500">{{ $t('shop.body.myAlbanyPark.subtitle') }}</p>
      </div>
      <v-sheet
          color="transparent"
          class=" mx-auto"
          elevation="0"
      >
        <v-slide-group
            class="pt-4 pb-0"
            selected-class="bg-primary"
            mandatory
            show-arrows
        >
          <v-slide-group-item
              v-for="item in photo_images"
              v-slot="{ isSelected, toggle, selectedClass }"
          >
            <v-card
                rounded="0"
                link
                :class="['ma-3', selectedClass]"
                color="transparent"
                width="250"
                height="250"
                :image="item.image"
            >
              <div class="d-flex justify-end">
                <v-icon class="pt-4 pr-4" color="black" icon="mdi-instagram" size="small"></v-icon>
              </div>
            </v-card>
          </v-slide-group-item>
        </v-slide-group>
      </v-sheet>
    </v-sheet>

    <!-- Share展示-->
    <v-sheet color="#F5F4F2">
      <div class="py-16  text-center font-buttershine-serif text-[#413e3a] text-3xl lg:text-4xl">
        {{ $t('shop.body.myAlbanyPark.unique_title') }}
      </div>
      <v-sheet color="transparent" class="d-flex justify-center pb-8">
        <v-sheet color="transparent" class="" width="1365">
          <v-row no-gutters class="d-flex flex-row-reverse">

            <v-col cols="12" md="6" class="d-flex">
              <v-card flat rounded="0" color="#EEE9E3" max-height="438" class="pa-12 d-flex align-center ">
                <div class="text-[#413E3A] ">
                  <h1 class="font-serif font-semibold lg:text-3xl text-xl py-8 md:text-2xl">
                    {{ $t('shop.body.myAlbanyPark.unique_subtitle') }}</h1>
                  <div class="text-sm leading-9">
                    <p class=" ">{{ $t('shop.body.myAlbanyPark.unique_text1') }}
                    </p>
                    <p class="py-10">{{ $t('shop.body.myAlbanyPark.unique_text2') }}</p>
                  </div>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="6" class="d-flex align-center">
              <v-img :src="HP5050" max-height="438" cover/>
            </v-col>
          </v-row>
        </v-sheet>
      </v-sheet>

      <!--不同的服务-->
      <v-sheet color="transparent" class="">
        <h1 class="py-16  text-center font-buttershine-serif text-[#413e3a] text-3xl lg:text-4xl">
          {{ $t('shop.body.myAlbanyPark.apart_title') }}
        </h1>
        <!--  四个图标-->
        <div class="d-flex justify-center pb-8">
          <v-sheet color="transparent">
            <v-row class="">
              <v-col v-for="item in image_icons" :key="item.id" class="d-flex justify-center pa-0">
                <v-card width="334" class="py-6 px-4 " color="transparent" flat>
                  <div class=" d-flex justify-center ">
                    <div class="pb-8">
                      <v-img cover width="96" :src="item.url"/>
                    </div>
                  </div>
                  <div>
                    <h1 class="pb-6 text-center text-[#413e3a] font-buttershine-serif">{{ item.title }}</h1>
                    <p class="text-center text-[#413e3a]">{{ item.text }}</p>
                  </div>
                </v-card>
              </v-col>
            </v-row>
          </v-sheet>
        </div>
      </v-sheet>

      <!--    图片-->
      <v-sheet color="transparent" class="py-8">
        <banner-image
            HeadImage="https://oss-cdn.tearful.cn/shop/banner/Homepage_img-DT-4N4A9235-ASE_v2-2500x1152_1984x928.jpg"
            SubImage="https://oss-cdn.tearful.cn/shop/banner/Homepage_img-MB-4N4A9235-ASE_v2-1500x1500_800x.webp"/>
      </v-sheet>
      <!--  七个推荐说明图标-->
      <v-sheet color="transparent">
        <h1 class="py-16  text-center font-buttershine-serif text-[#413e3a] text-3xl lg:text-4xl">
          {{ $t('shop.body.myAlbanyPark.icon_title') }}
        </h1>

        <div class="d-flex justify-center">
          <v-sheet color="transparent" max-width="1100">
            <div class="pb-8 ">
              <v-row class="">
                <v-col v-for="item in qualityIcons" :key="item.id" class="d-flex justify-center pa-0">
                  <v-card class="pa-16" color="transparent"
                          flat>
                    <div class="d-flex justify-center">
                      <div class="pb-8">
                        <v-img cover width="118" :src="item.url"/>
                      </div>
                    </div>
                    <div>
                      <h1 class="text-center text-[#413e3a]"> {{ item.title }}</h1>
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
          </v-sheet>
        </div>
      </v-sheet>
    </v-sheet>

    <!--      动态图片-->
    <v-sheet color="#EEE9E3" class="py-16 d-md-flex justify-md-center">
      <v-sheet color="transparent" max-width="1365">
        <v-row no-gutters justify="center">
          <v-col cols="12" md="6" class="d-flex align-center px-2 px-md-12">
            <v-img src="https://oss-cdn.tearful.cn/shop/home/<USER>"
                   cover/>
          </v-col>
          <v-col cols="12" md="6" class="">
            <v-card flat rounded="0" color="transparent" class="px-4 px-md-12 d-flex align-center ">
              <div class="text-[#413E3A] ">
                <h1 class="font-serif font-semibold text-2xl py-8">{{ $t('shop.body.assemble_card.assemble_title') }}</h1>
                <div class="text-sm leading-9">
                  <p class=" ">{{ $t('shop.body.assemble_card.assemble_text1') }}
                  </p>
                  <p class="pt-10">{{ $t('shop.body.assemble_card.assemble_text2') }}</p>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
        <v-row no-gutters class="pt-8  mt-md-16 pt-md-8 flex-row-reverse">
          <v-col cols="12" md="6" class="d-flex align-center px-2 px-md-12">
            <v-img
                src="https://oss-cdn.tearful.cn/shop/home/<USER>"
                width="667" cover/>
          </v-col>
          <v-col cols="12" md="6" class="d-flex px-4 px-md-12">
            <v-card flat rounded="0" color="transparent" class=" d-flex align-center ">
              <div class="text-[#413E3A] ">
                <h1 class="font-serif font-semibold text-2xl py-8">{{ $t('shop.body.assemble_card.coziness_title') }}</h1>
                <div class="text-sm leading-9">
                  <p class=" ">{{ $t('shop.body.assemble_card.coziness_text') }}
                  </p>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </v-sheet>
    </v-sheet>

    <!--    两个沙发图标-->
    <v-sheet color="#F5F4F2" class="">
      <sofa-show :Items="buyers"/>
    </v-sheet>

    <!--    流行沙发轮播图-->
    <v-sheet class="pb-8 pt-16 " color="#EEE9E3">
      <h1 class=" text-center  font-buttershine-serif text-[#413e3a] text-3xl lg:text-4xl">Most Popular</h1>

      <v-sheet
          color="transparent"
          class=" mx-auto pb-8 w-[100%] sm:w-[491px] md:w-[880px] xl:w-[1265px]"
          elevation="0"

      >
        <v-slide-group
            class="pt-4 pb-0"
            selected-class="bg-primary"
            mandatory
            show-arrows
        >
          <v-slide-group-item
              v-for="item in populars"
              v-slot="{ isSelected, toggle, selectedClass }"
          >
            <v-card
                flat
                rounded="0"
                link
                :class="['ma-3', selectedClass]"
                color="transparent"
                class="pb-8 sm:w-[363px] w-[340px]"
            >
              <v-img width="85" :src="item.saleUrl"></v-img>
              <v-img :src="item.imageUrl"></v-img>
              <div class="d-flex flex-column text-center pt-8">
                <h1 class="font-semibold py-2">{{ item.title }}</h1>
                <p>
                  <span class="text-sm font-thin px-1">from</span>
                  <span class="font-semibold px-1">{{ item.discount }}</span>
                  <span class="text-decoration-line-through text-[#C1C1C1]">{{ item.price }}</span></p>
              </div>
            </v-card>
          </v-slide-group-item>
        </v-slide-group>
      </v-sheet>

    </v-sheet>


    <!--    三个品牌信誉-->
    <v-sheet class="d-flex flex-row justify-center py-16" color="#F5F4F2">
      <div>
        <v-row>
          <v-col v-for="item in brandIcons" :key="item.id" class="d-flex justify-center pa-0">
            <v-card flat color="transparent" class="px-6 py-4" max-width="445" min-width="334">
              <div class="d-flex justify-center py-4">
                <div>
                  <v-img width="108" :src="item.imageUrl"></v-img>
                </div>
              </div>
              <div class="text-center text-[#413e3a]">
                <h1 class="py-4 font-semibold">{{ item.title }}</h1>
                <p class="text-md font-thin">{{ item.text }}</p>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-sheet>
    <!--    <v-card class="py-8">-->
    <!--      屏幕宽度:{{ screenSize.width }}-->
    <!--      屏幕高度:{{ screenSize.height }}-->
    <!--      &lt;!&ndash;      <div>&ndash;&gt;-->
    <!--      &lt;!&ndash;        <v-btn @click="setLocale('en')">英文</v-btn>&ndash;&gt;-->
    <!--      &lt;!&ndash;        <v-btn @click="setLocale('zh')">中文</v-btn>&ndash;&gt;-->
    <!--      &lt;!&ndash;        <p>{{ $tm('welcome') }}</p>&ndash;&gt;-->
    <!--      &lt;!&ndash;        <p>{{ $tm('messages') }}</p>&ndash;&gt;-->
    <!--      &lt;!&ndash;      </div>&ndash;&gt;-->
    <!--    </v-card>-->

    <!-----------------------------------------------------------------------------------------分隔符-------------------------------------------------------------------------------------------------------------->

    <!--    视频插件-->
    <!--        <div id="mse"></div>-->
  </v-app>

</template>

<style scoped>


</style>