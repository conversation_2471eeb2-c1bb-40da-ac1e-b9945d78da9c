<script setup lang="ts">
// 用tm代替t，因为tm是响应式的而且可以解析对象
import {ref,computed} from "vue";
import {navigateTo} from "nuxt/app";
const {locale, setLocale, tm} = useI18n()

/**
 * 路由跳转到Contact-us页面
 */
const toContactPage = async () => {
  await navigateTo('/contact-us')
}

/**
 * 四个的主题
 * 0  PRODUCTS
 * 1  ASSEMBLY
 * 2  ORDERING & PAYMENT
 * 3  SHIPPING & RETURNS
 */
let selectItem = ref(0);

/**
 * 切换不同的“子路由”
 */
const questions = computed(() => {
  switch (selectItem.value) {
    case 0:
      return tm("faq.questions.products");
    case 1:
      return tm("faq.questions.assembly");
    case 2:
      return tm("faq.questions.ordering");
    case 3:
      return tm("faq.questions.shipping");
  }
})
const seo_title = computed(() => {
  return tm("faq.seo.title");
})
const seo_ogDescription = computed(() => {
  return tm("faq.seo.ogDescription");
})

// SEO 会根据中英文切换做不同的SEO
useSeoMeta({
  title: seo_title,
  ogDescription: seo_ogDescription,
})

</script>

<template>
  <v-app class="pt-16">
    <v-sheet color="#F5F4F2" class="pt-8">
      <v-sheet color="transparent" class="pt-8 pb-6 text-h3 d-flex justify-center">
        <h1 class="text-[#413E3A]">
          FAQ
        </h1>
      </v-sheet>
      <!--画笔-->
      <v-sheet color="transparent" class="d-flex justify-center">
        <v-sheet color="transparent" width="180" height="11">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 254.81 15.88">
            <title>Asset 3</title>
            <g id="Layer_2" data-name="Layer 2">
              <g id="Layer_1-2" data-name="Layer 1">
                <path class="cls-1-main text-[#413E3A]"
                      d="M4.05,15.38C18.22,9.28,33.39,6,48.7,4.43a286.36,286.36,0,0,1,48-.36c17.35,1.15,34.61,3.38,51.88,5.34,17.61,2,35.27,3.78,53,4a191.5,191.5,0,0,0,24.89-1c7.61-.89,15.35-2.55,22.28-5.93a48.9,48.9,0,0,0,4.95-2.77c.8-.52,1.7-1.59.52-2.24s-3.1-.1-4.11.55c-5.9,3.84-12.75,5.76-19.66,6.8a157.82,157.82,0,0,1-23.9,1.34c-17.54-.07-35-1.73-52.45-3.69s-34.62-4.2-52-5.48A309.62,309.62,0,0,0,53.48.89C37.69,2.22,22,5.24,7.12,10.87c-1.8.68-3.58,1.41-5.35,2.17C1,13.38-.57,14.32.22,15.34s3,.42,3.83,0Z"></path>
              </g>
            </g>
          </svg>
        </v-sheet>
      </v-sheet>
      <!--  搜索栏-->
      <v-sheet color="transparent pt-8">
        <v-card
            class="mx-auto"
            color="transparent"
            elevation="0"
            max-width="480"
            flat
        >
          <v-card-text>
            <v-text-field
                append-inner-icon="mdi-magnify"
                density="compact"
                label="Start typing"
                variant="solo"
                hide-details
                single-line
            ></v-text-field>
          </v-card-text>
        </v-card>
      </v-sheet>
      <!--四个按钮-->
      <v-sheet color="transparent" class="d-flex justify-center py-16">
        <div
            class="select-none grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-2"
        >
          <label
              class="radio flex flex-grow items-center justify-center rounded-sm p-1 cursor-pointer"
              @click="selectItem = 0"
          >
            <input
                type="radio"
                name="radio"
                value="html"
                class="peer hidden"
                checked=""
            />
            <span
                class="text-[#413E3A] sm:w-80 w-96 py-3 font-weight-bold text-center tracking-widest border-[1px] border-[#EEE9E3] peer-checked:bg-[#F5F4F2] peer-checked:border-[1px] peer-checked:border-[#C7B7AA] hover:bg-[#F5F4F2] hover:border-[1px] hover:border-[#C7B7AA] bg-[#EEE9E3] p-2 rounded-md transition duration-150 ease-in-out"
            >{{ $t('faq.questions.products.title') }}</span
            >
          </label>

          <label
              class="radio flex flex-grow items-center justify-center rounded-sm p-1 cursor-pointer"
              @click="selectItem=1"
          >
            <input
                type="radio"
                name="radio"
                value="html"
                class="peer hidden"
            />
            <span
                class="text-[#413E3A] sm:w-80 w-96 py-3 font-weight-bold text-center tracking-widest border-[1px] border-[#EEE9E3] peer-checked:bg-[#F5F4F2] peer-checked:border-[1px] peer-checked:border-[#C7B7AA] hover:bg-[#F5F4F2] hover:border-[1px] hover:border-[#C7B7AA] bg-[#EEE9E3] p-2 rounded-md transition duration-150 ease-in-out"
            >{{ $t('faq.questions.assembly.title') }}</span
            >
          </label>

          <label
              class="radio flex flex-grow items-center justify-center rounded-sm p-1 cursor-pointer"
              @click="selectItem=2"
          >
            <input
                type="radio"
                name="radio"
                value="html"
                class="peer hidden"
            />
            <span
                class="text-[#413E3A] sm:w-80 w-96 py-3 font-weight-bold text-center tracking-widest border-[1px] border-[#EEE9E3] peer-checked:bg-[#F5F4F2] peer-checked:border-[1px] peer-checked:border-[#C7B7AA] hover:bg-[#F5F4F2] hover:border-[1px] hover:border-[#C7B7AA] bg-[#EEE9E3] p-2 rounded-md transition duration-150 ease-in-out"
            >{{ $t('faq.questions.ordering.title') }}</span
            >
          </label>

          <label
              class="radio flex flex-grow items-center justify-center rounded-sm p-1 cursor-pointer"
              @click="selectItem=3"
          >
            <input
                type="radio"
                name="radio"
                value="html"
                class="peer hidden"/>
            <span
                class="text-[#413E3A] sm:w-80 w-96 py-3 font-weight-bold text-center tracking-widest border-[1px] border-[#EEE9E3] peer-checked:bg-[#F5F4F2] peer-checked:border-[1px] peer-checked:border-[#C7B7AA] hover:bg-[#F5F4F2] hover:border-[1px] hover:border-[#C7B7AA] bg-[#EEE9E3] p-2 rounded-md transition duration-150 ease-in-out"
            >{{ $t('faq.questions.shipping.title') }}</span>
          </label>
        </div>
      </v-sheet>

      <!-- 问题列表-->
      <v-sheet color="transparent" class="pb-16 text-4xl">
        <div>
          <h1 class="text-center text-[#413E3A]">{{ questions.title }}</h1>
        </div>
        <div class="d-flex justify-center py-10">
          <v-sheet class=" w-[90%] md:w-[85%] lg:w-[75%] xl:w-[65%] 2xl:w-[45%] d-flex justify-center">
            <v-expansion-panels
                elevation="0"
                variant="accordion"
            >
              <v-expansion-panel
                  v-for="item in questions.items"
                  :key="item.id"
                  class="border-[2px] border-[#EEE9E3]"
              >
                <v-expansion-panel-title color="#EEE9E3">
                  <p class="text-[#413E3A]">
                    {{ item.question }}
                  </p>
                  <template v-slot:actions="{ expanded }">
                    <v-icon color="#413E3A" :icon="expanded ? 'mdi-minus' : 'mdi-plus'"></v-icon>
                  </template>
                </v-expansion-panel-title>
                <v-expansion-panel-text class=" text-body-2 text-[#413E3A] bg-[#F5F4F2]">
                  <p class="py-8">
                    {{ item.answer }}
                  </p>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-sheet>
        </div>
      </v-sheet>

      <!--  联系我们按钮-->
      <v-sheet color="#EEE9E3" class="grid grid-cols-1 py-[90px]">
        <h1 class="text-center pb-8 font-normal text-2xl text-[#413E3A]">
          {{ $t('faq.contact') }}
        </h1>
        <div class="d-flex justify-center" @click="toContactPage">
          <span
              class="cursor-pointer text-center hover:bg-[#937C67] border-[1px] px-7 py-3 bg-[#87725E] text-[#fff] pointer">
          {{ $t('faq.button') }}
          </span>
        </div>
      </v-sheet>

    </v-sheet>
  </v-app>
</template>

<style scoped>

</style>