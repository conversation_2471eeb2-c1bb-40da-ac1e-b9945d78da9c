<template>

  <div class="mt-8 bg-[#f5f4f2]">

<!--    <youtube-player :overlay="overlay" :video-url="VideoUrl"/>-->


    <v-sheet class="d-flex justify-center py-16" color="#f5f4f2">
      <!--  左边的图-->
      <!--      滑动时候图片静态置顶可以了解一下position-sticky这个属性（tailwindcss）-->
      <v-img class="mx-16 position-sticky top-16"
             max-width="1105"
             min-width="490"
             max-height="737"
             cover
             src="https://oss-cdn.tearful.cn/shop/file/KovaBouquet_0800_e42e9761-628c-48e3-9ccd-29ce81e273ca_1105x.webp"
      ></v-img>

      <!--  右边的文字-->
      <v-sheet color="transparent" max-width="475" class="">
        <div class="text-[#413e3a] text-h6">
          Kova L-S<PERSON>pe + Ottoman
        </div>
        <div class="text-[#413e3a] my-2">
          $3260 <a class="font-weight-thin text-decoration-line-through mx-2">$5015</a>
        </div>

        <div class="text-[#413e3a] text-body-2">
          Starting at $243/mo or 0% APR with Affirm. Check your purchasing power
        </div>
        <!--        分界线-->
        <v-divider class="border-opacity-100 my-6" :thickness="2" color="red"></v-divider>

        <!--      加入购物车  按钮-->
        <v-btn block elevation="0" rounded="0" height="63" color="#786554" class="font-weight-bold my-8">ADD TO CART
        </v-btn>
        <div class="text-sm pb-6">
          Ships: October 25
        </div>

        <div class="grid grid-cols-2 gap-x-2 gap-y-4 font-weight-thin text-xs">
          <div class="d-flex align-center" v-for="item in style_texts">
            <v-img src="@/assets/icon/Check_Icon_901594d7-392b-4d18-aa11-1708e1219f7a.webp" inline height="20"
                   width="20" class="">
            </v-img>
            <div class="ml-3">
              {{ item.text }}
            </div>
          </div>
        </div>

        <!--        分界线-->
        <v-divider class="border-opacity-100 my-6" :thickness="2" color="#786554"></v-divider>

        <!--        描述-->
        <div class="text-sm pb-6">
          Description
        </div>

        <div class="text-[#413E3A] font-weight-thin text-sm leading-relaxed">
          Incredibly cozy and deep, the Kova was made to unwind, relax, and kickyour feet up. The new cloud-soft,
          hypoallergenic and 100% vegancushions provide exceptional sink-in comfort for the whole family.Combine Kova's
          modular pieces to create endless configurations to fityour home and your lifestyle.
        </div>

        <!--特征-->
        <div class="text-[#413E3A] my-4">
          Features:
        </div>

        <ul class="ml-6 list-outside list-disc text-[#413E3A] font-weight-thin text-sm" v-for="item in features_texts">
          <li class="leading-relaxed py-2">
            {{ item.text }}
          </li>
        </ul>
      </v-sheet>
    </v-sheet>

    <!--      视频-->
    <v-sheet class="py-16" color="transparent">
      <v-img
          max-height="500"
          cover
          class="align-center "
          src="https://oss-cdn.tearful.cn/shop/file/AP_PDP_assembly_cover_1920x544.webp">
        <div class="d-flex justify-center ">
          <v-icon @click="onPlay('https://www.youtube.com/watch?v=P3BCTNewvhk')"  class="" color="#fff"
                  icon="mdi-play-circle" size="88"></v-icon>
        </div>

      </v-img>
    </v-sheet>
  </div>
</template>

<script setup lang="ts">

const {locale, setLocale, tm} = useI18n()

const style_texts = computed(() => {
  return tm("shop.collections.kova.october")
})
const features_texts = computed(() => {
  return tm("shop.collections.kova.features")
})

/**
 * 视频地址
 */
const VideoUrl = ref("")

/**
 * 是否显示遮罩层
 */
const overlay = ref(false)

/**
 * 点击播放视频
 * @param url 视频地址
 */
const onPlay = (url: string) => {
  VideoUrl.value = url;
  overlay.value = true;
}


</script>

<style scoped>

</style>