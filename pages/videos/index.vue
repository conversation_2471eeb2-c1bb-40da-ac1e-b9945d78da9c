<script setup lang="ts">
import {onMounted, ref} from "vue";
// 用tm代替t，因为tm是响应式的而且可以解析对象
const {locale, setLocale, tm} = useI18n()


const routeData = useRoute()
onMounted(() => {
  // console.log("videos页面接受路由参数query", routeData)
})

const seo_title = computed(() => {
  return tm("videos.seo.title");
})
const seo_ogDescription = computed(() => {
  return tm("videos.seo.ogDescription");
})

// SEO 会根据中英文切换做不同的SEO
useSeoMeta({
  title: seo_title,
  ogDescription: seo_ogDescription,
})


/**
 * 视频地址
 */
const VideoUrl = ref("")

/**
 * 图片是否加载完成
 */
const home_image1_load = ref(false)

/**
 * 是否显示遮罩层
 */
const overlay = ref(false)

/**
 * 点击播放视频
 * @param url 视频地址
 */
const onPlay = (url: string) => {
  VideoUrl.value = url;
  overlay.value = true;
}

/**
 * 图片加载完成回调
 * @param val 图片地址
 */
const image_load = (val: any) => {
  home_image1_load.value = true;
}

/**
 * 卡片组循环
 */
const videos = computed(() => {
  return tm("videos.items")
})

</script>

<template>
  <v-app>
    <v-sheet color="#F5F4F2">

      <!--    遮罩层-->
      <v-overlay v-model="overlay" class="d-flex justify-center align-center">
        <!--接入youtobe的视频 -->
        <v-sheet>
          <video-youtube :video-url="VideoUrl" width="1500" height="800"/>
        </v-sheet>
      </v-overlay>

      <!--主图-->
      <v-sheet class="pt-[72px]" color="transparent">
        <banner-image
            HeadImage="https://oss-cdn.tearful.cn/shop/videos/barton-sofa_tan-vegan-leather-stain-resistant_1.webp"/>
      </v-sheet>

      <v-sheet color="transparent" class="py-16 d-md-flex justify-md-center">
        <v-sheet color="transparent" max-width="1020">
          <div v-for="item in videos" :key="item.id">
            <!--  item.id % 2 === 0 如果是双数行，就将图片和文字倒反-->
            <v-row no-gutters class="py-8" :class="item.id % 2 === 0 ? 'flex-row-reverse':''">
              <!--   图片-->
              <v-col cols="12" md="6" class="d-flex align-center justify-center ">
                <v-card flat rounded="0" width="450" height="332"
                        class="d-flex justify-center align-center"
                        :image="item.imageUrl">
                  <v-icon @click="onPlay(item.videoUrl)" color="#fff"
                          icon="mdi-play-circle" size="88"></v-icon>
                </v-card>
              </v-col>
              <!--   文字-->
              <v-col cols="12" md="6" class="d-flex">
                <v-card flat rounded="0" color="transparent" class="px-6 d-flex align-center ">
                  <div class="text-[#413E3A] ">
                    <h1 class="font-serif font-semibold text-xl pt-8 pb-4">{{ item.title }}</h1>
                    <div class="text-xs font-weight-medium leading-6">
                      <p class=" ">{{ item.text }} </p>
                    </div>
                  </div>
                </v-card>
              </v-col>
            </v-row>
          </div>
        </v-sheet>
      </v-sheet>
    </v-sheet>
  </v-app>
</template>

<style scoped>

</style>