<!--首部栏组件-->
<script setup lang="ts">
import {ref} from "vue";
import {storeToRefs} from "pinia";

const userStore = useUserPinia()
const {drawer} = storeToRefs(userStore);
const {locale, setLocale} = useI18n()

</script>

<template>
  <div>
    <v-app-bar
        color="#F5F4F2"
        height="72"
        scroll-behavior="elevate hide"
        scroll-threshold="135">
      <div class="w-full d-flex justify-space-evenly align-center">
        <!--      侧边栏按钮 d-sm-flex d-md-none表示仅在sm尺寸(包括)以下可见-->
        <div class="d-sm-flex d-md-none">
          <v-btn class="pa-0" color="#786554" icon="mdi-format-list-bulleted" @click.stop="drawer = !drawer"></v-btn>
        </div>

        <!--logo图标-->
        <div class="cursor-pointer">
          <NuxtLink to="/">
            <v-img
                class="w-[240px] md:w-[270px]"
                height="25"
                src="https://oss-cdn.tearful.cn/shop/albany-park-brown-logo_270x25.svg"
            ></v-img>
          </NuxtLink>
        </div>

        <!--网页端导航栏（移动端的导航栏在底部）-->
        <NavigationBar class="d-none d-sm-none d-md-flex"/>

        <!--图标类-->
        <div class="d-flex flex-row lg:w-[270px]">

          <!-- Gitee图标-->
          <div class="d-none d-sm-none d-md-flex">
            <a target="_blank" href="https://gitee.com/zzuli_huahua/shop-nuxt3">
              <v-card rounded="circle" flat link color="transparent" class="pa-3">
                <v-img width="26" height="26" src="https://oss-cdn.tearful.cn/shop/icons/SimpleIconsGitee.png">
                </v-img>
              </v-card>
            </a>
          </div>
          <!-- GitHub图标-->
          <!--          <v-btn class="d-none d-sm-none d-md-flex" color="back" icon="mdi-github"></v-btn>-->

          <!-- 中英文切换-->
          <v-menu>
            <template v-slot:activator="{ props }">
              <v-btn color="#786554" icon="mdi-translate" v-bind="props"></v-btn>
            </template>
            <v-list>
              <v-list-item @click="setLocale('en')">
                <v-list-item-title :class="locale==='en'?'text-[#177FD5]':'text-[#413E3A]'">English</v-list-item-title>
              </v-list-item>
              <v-list-item @click="setLocale('zh')">
                <v-list-item-title :class="locale==='zh'?'text-[#177FD5]':'text-[#413E3A]'">简体中文</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>

    </v-app-bar>
    <!--    移动端侧边栏抽屉 开发时候需要注释这一行（不然开发环境会卡，这是nuxt3和vuetify3部分组件不兼容的结果，希望官方后期能解决掉），打包的时候去掉注释即可-->
            <navigation-drawer/>
  </div>

</template>

<style scoped>
</style>