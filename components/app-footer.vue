<!--页脚组件-->
<template>
  <v-footer
      color="#786554"
      class="py-4 d-flex justify-center">
    <v-sheet color="transparent">
      <v-row no-gutters class="pt-8">
        <!-- order="2" order-md="1"解释：屏幕（宽度）从最小（640px）到大开始，order表示默认的时候与其他的col元素排名，order-md表示当屏幕大于等于md（768px）的时候order的变化。-->
        <!-- 比如order="2" order-md="1"，解释：初始值元素排第一，当屏幕大于等于768px，就变成排第三。-->
        <!--  参考文档 https://developer.mozilla.org/en-US/docs/Web/CSS/order-->
        <v-col order="2" order-md="1" cols="6" md="2" class="">
          <div class="d-flex justify-center max-w-52">
            <div class="text-sm font-thin leading-7">
              <h1 class="font-weight-bold">Products</h1>
              <h1>Sofas</h1>
              <h1>Sectionals</h1>
              <h1>Loveseats</h1>
              <h1>Sleeper Sofas</h1>
              <h1>Armchairs</h1>
              <h1>Ottomans</h1>
              <h1>Best Sellers</h1>
            </div>
          </div>
        </v-col>
        <v-col order="2" order-md="1" cols="6" md="2" class="">
          <div class="d-flex justify-center pb-8">
            <div class="text-sm font-thin leading-7">
              <h1 class="font-weight-bold">Support</h1>
              <h1>Order Free Swatches</h1>
              <h1>Assembly</h1>
              <h1>FAQs</h1>
              <h1>Financing</h1>
              <h1>Warranty</h1>
              <h1>Shipping & Delivery</h1>
              <h1>Returns</h1>
              <h1>Contact Us</h1>
              <h1>Do Not Sell My</h1>
              <h1>Personal Information</h1>
            </div>
          </div>
        </v-col>
        <!--   居中-->
        <v-col order="1" order-md="2" cols="12" md="4" class="px-4 pb-8">
          <v-sheet color="transparent" class="text-center">
            <h1 class="text-h5 pt-4 pb-8 text-xxl-h4">A cozy home begins here.™</h1>
            <h1 class="pb-2  text-body-1 text-xxl-h5">Join the Club!</h1>
            <h1 class="text-xs font-thin">Sign up to enjoy discounts, exclusive sales and the latest news!</h1>
            <!--  搜索栏-->
            <div class="d-flex flex-row pt-4 px-4 ">
              <v-text-field
                  bg-color="#fff"
                  rounded="0"
                  color="#EEE9E3"
                  hide-details="auto"
                  label="Email Address"
              ></v-text-field>
              <div class="d-flex align-center px-6 bg-[#EEE9E3] cursor-pointer">
                <p class="text-center text-sm text-[#786554]">Sign Me Up</p>
              </div>
            </div>
          </v-sheet>
        </v-col>
        <v-col order="3" cols="6" md="2" class="w-52">
          <div class="d-flex justify-center">
            <div class="text-sm font-thin leading-7">
              <h1 class="font-weight-bold">About</h1>
              <h1>Our Story</h1>
              <h1>Jobs</h1>
              <h1>B2B / Wholesale</h1>
            </div>
          </div>
        </v-col>
        <v-col order="3" cols="6" md="2" class="">
          <div class="d-flex justify-center">
            <div class="text-sm font-thin leading-7">
              <h1 class="font-weight-bold">Need Help?</h1>
              <h1>Contact Us</h1>
              <h1>Need a Hand?</h1>
              <h1>Spread the love!</h1>
              <h1>Refer & Earn</h1>
            </div>
            <div>
            </div>
          </div>
        </v-col>
        <v-col order="5" cols="12" class="pt-12">
          <div class="d-flex flex-row justify-center text-xs font-weight-light divide-x-2 ">
            <p class="px-2">
              © 2024 ALBANY PARK. ALL RIGHTS RESERVED.
            </p>
            <p class="px-2 cursor-pointer">
              PRIVACY NOTICE
            </p>
            <v-divider vertical></v-divider>
            <p class="px-2 cursor-pointer">
              TERMS OF USE
            </p>
            <v-divider vertical></v-divider>
            <p class="px-2 cursor-pointer">
              ACCESSIBILITY
            </p>
          </div>
          <!--          备案号-->
          <v-sheet color="transparent" class="d-flex justify-center my-4">
            <img src="https://beian.mps.gov.cn/web/assets/logo01.6189a29f.png" class="w-full" style="width: 24px;">
            <a class="pr-10" href="https://beian.mps.gov.cn/#/query/webSearch?code=44030002005331" target="_blank" data-v-72957a96="">粤公网安备44030002005331号 </a>
            <a href="https://beian.miit.gov.cn/" target="_blank">桂ICP备2022003898号-3</a>
          </v-sheet>
        </v-col>
      </v-row>
    </v-sheet>
  </v-footer>

</template>

<script setup lang="ts">
import {ref} from "vue";

</script>

<style scoped>

</style>
