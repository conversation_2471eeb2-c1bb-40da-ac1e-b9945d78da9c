{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@mdi/font": "^7.4.47", "@pinia/nuxt": "^0.5.1", "core-js": "^3.37.0", "nuxt": "^3.11.2", "vue": "^3.4.21", "vue-i18n": "^9.13.1", "vue-router": "^4.3.0", "xgplayer": "^3.0.17"}, "devDependencies": {"@nuxtjs/i18n": "^8.0.0", "@nuxtjs/tailwindcss": "^6.12.0", "@pinia-plugin-persistedstate/nuxt": "^1.2.0", "sass": "^1.77.1", "vite-plugin-vuetify": "^2.0.3", "vuetify": "^3.6.3"}}